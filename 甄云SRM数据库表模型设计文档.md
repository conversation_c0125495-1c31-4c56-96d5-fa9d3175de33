# 甄云SRM数据库表模型设计文档

## 1. 概述

本文档基于甄云SRM系统的API接口文档分析，设计了完整的数据库表模型。该系统主要涵盖供应商关系管理(SRM)的核心业务功能，包括询价管理、采购管理、供应商生命周期管理、专家评分等模块。

## 2. 核心业务实体分析

### 2.1 主要业务对象
根据API文档分析，系统包含以下核心业务对象：

1. **询价管理模块**
   - 询价单头 (SRM_SSRC_RFX_HEADER)
   - 询价单行-物料 (SRM_SSRC_RFX_LINE_ITEM)
   - 询价单行-供应商 (SRM_SSRC_RFX_LINE_SUPPLIER)

2. **专家评分模块**
   - 专家库表 (SRM_SSRC_EXPERT)
   - 专家评分专家表 (SRM_SSRC_EVALUATE_EXPERT)
   - 专家评分要素表 (SRM_SSRC_EVALUATE_INDIC)

3. **采购管理模块**
   - 采购申请头 (SRM_SPRM_PR_HEADER)
   - 采购申请行 (SRM_SPRM_PR_LINE)
   - 采购协议头 (SRM_SPCM_PC_HEADER)

4. **供应商管理模块**
   - 供应商生命周期 (SRM_SSLM_LIFE_CYCLE)
   - 供应商生命周期阶段配置 (SRM_SSLM_LIFE_CYCLE_STAGES)
   - 潜在升级申请单 (SRM_SSLM_POTENTIAL_HEADER)

5. **基础数据模块**
   - 物料 (SRM_SMDM_ITEM)
   - 计量单位定义 (SRM_SMDM_UOM)
   - 自主品类 (SRM_SMDM_ITEM_CATEGORY)

6. **组织架构模块**
   - 公司信息 (SRM_HPFM_COMPANY)
   - 组织 (SRM_HPFM_UNIT)
   - 采购组织 (SRM_HPFM_PURCHASE_ORGANIZATION)
   - 业务实体 (SRM_HPFM_OPERATION_UNIT)

7. **用户管理模块**
   - 子账户 (SRM_IAM_USER)

## 3. 数据库表设计

### 3.1 询价管理表

#### 3.1.1 询价单头表 (ssrc_rfx_header)
```sql
CREATE TABLE ssrc_rfx_header (
    rfx_header_id VARCHAR(50) PRIMARY KEY COMMENT '询价单头ID',
    domain_id VARCHAR(50) NOT NULL COMMENT '域ID',
    rfx_num VARCHAR(60) NOT NULL COMMENT 'RFX单号',
    rfx_status VARCHAR(30) NOT NULL COMMENT '状态',
    source_category VARCHAR(20) NOT NULL COMMENT '寻源类型',
    quotation_type VARCHAR(20) NOT NULL COMMENT '报价方式',
    round_number INT NOT NULL DEFAULT 1 COMMENT '版本',
    adjust_times INT DEFAULT 0 COMMENT '调整次数',
    company_id VARCHAR(50) NOT NULL COMMENT '公司ID',
    application_scope_flag TINYINT(1) DEFAULT 0 COMMENT '适用范围标识',
    auto_defer_flag TINYINT(1) DEFAULT 0 COMMENT '是否启用自动延时',
    check_item_release_flag TINYINT(1) DEFAULT 0 COMMENT '释放未中标申请数量',
    closed_flag TINYINT(1) DEFAULT 0 COMMENT '关闭标识',
    current_sequence_num INT COMMENT '当前专家评分组别',
    only_allow_all_win_bids TINYINT(1) DEFAULT 0 COMMENT '仅允许整单中标',
    sealed_quotation_flag TINYINT(1) DEFAULT 0 COMMENT '密封报价标志',
    source_creation_date DATETIME COMMENT '单据创建时间',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    remark TEXT COMMENT '备注',
    
    INDEX idx_rfx_num (rfx_num),
    INDEX idx_rfx_status (rfx_status),
    INDEX idx_company_id (company_id),
    INDEX idx_creation_date (creation_date),
    FOREIGN KEY (company_id) REFERENCES hpfm_company(company_id)
) COMMENT='询价单头表';
```

#### 3.1.2 询价单行-物料表 (ssrc_rfx_line_item)
```sql
CREATE TABLE ssrc_rfx_line_item (
    rfx_line_item_id VARCHAR(50) PRIMARY KEY COMMENT '询价单行物料ID',
    rfx_header_id VARCHAR(50) NOT NULL COMMENT '询价单头ID',
    item_id VARCHAR(50) NOT NULL COMMENT '物料ID',
    line_number INT NOT NULL COMMENT '行号',
    quantity DECIMAL(18,6) COMMENT '数量',
    uom_id VARCHAR(50) COMMENT '计量单位ID',
    required_date DATE COMMENT '需求日期',
    delivery_address VARCHAR(500) COMMENT '交货地址',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    
    INDEX idx_rfx_header_id (rfx_header_id),
    INDEX idx_item_id (item_id),
    INDEX idx_line_number (line_number),
    FOREIGN KEY (rfx_header_id) REFERENCES ssrc_rfx_header(rfx_header_id),
    FOREIGN KEY (item_id) REFERENCES smdm_item(item_id),
    FOREIGN KEY (uom_id) REFERENCES smdm_uom(uom_id)
) COMMENT='询价单行-物料表';
```

#### 3.1.3 询价单行-供应商表 (ssrc_rfx_line_supplier)
```sql
CREATE TABLE ssrc_rfx_line_supplier (
    rfx_line_supplier_id VARCHAR(50) PRIMARY KEY COMMENT '询价单行供应商ID',
    rfx_header_id VARCHAR(50) NOT NULL COMMENT '询价单头ID',
    rfx_line_item_id VARCHAR(50) NOT NULL COMMENT '询价单行物料ID',
    supplier_id VARCHAR(50) NOT NULL COMMENT '供应商ID',
    quoted_price DECIMAL(18,6) COMMENT '报价',
    currency_code VARCHAR(10) COMMENT '币种',
    quote_status VARCHAR(20) COMMENT '报价状态',
    win_bid_flag TINYINT(1) DEFAULT 0 COMMENT '中标标识',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    
    INDEX idx_rfx_header_id (rfx_header_id),
    INDEX idx_rfx_line_item_id (rfx_line_item_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_win_bid_flag (win_bid_flag),
    FOREIGN KEY (rfx_header_id) REFERENCES ssrc_rfx_header(rfx_header_id),
    FOREIGN KEY (rfx_line_item_id) REFERENCES ssrc_rfx_line_item(rfx_line_item_id)
) COMMENT='询价单行-供应商表';
```

### 3.2 专家评分管理表

#### 3.2.1 专家库表 (ssrc_expert)
```sql
CREATE TABLE ssrc_expert (
    expert_id VARCHAR(50) PRIMARY KEY COMMENT '专家ID',
    expert_code VARCHAR(30) NOT NULL COMMENT '专家编码',
    expert_name VARCHAR(100) NOT NULL COMMENT '专家姓名',
    expert_type VARCHAR(20) COMMENT '专家类型',
    professional_field VARCHAR(200) COMMENT '专业领域',
    title VARCHAR(50) COMMENT '职称',
    company_id VARCHAR(50) COMMENT '所属公司ID',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    enabled_flag TINYINT(1) DEFAULT 1 COMMENT '启用标识',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    
    UNIQUE KEY uk_expert_code (expert_code),
    INDEX idx_expert_name (expert_name),
    INDEX idx_expert_type (expert_type),
    INDEX idx_company_id (company_id),
    FOREIGN KEY (company_id) REFERENCES hpfm_company(company_id)
) COMMENT='专家库表';
```

#### 3.2.2 专家评分专家表 (ssrc_evaluate_expert)
```sql
CREATE TABLE ssrc_evaluate_expert (
    evaluate_expert_id VARCHAR(50) PRIMARY KEY COMMENT '评分专家ID',
    rfx_header_id VARCHAR(50) NOT NULL COMMENT '询价单头ID',
    expert_id VARCHAR(50) NOT NULL COMMENT '专家ID',
    sequence_num INT NOT NULL COMMENT '评分组别',
    evaluate_status VARCHAR(20) COMMENT '评分状态',
    total_score DECIMAL(8,2) COMMENT '总分',
    evaluate_date DATETIME COMMENT '评分日期',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    
    INDEX idx_rfx_header_id (rfx_header_id),
    INDEX idx_expert_id (expert_id),
    INDEX idx_sequence_num (sequence_num),
    FOREIGN KEY (rfx_header_id) REFERENCES ssrc_rfx_header(rfx_header_id),
    FOREIGN KEY (expert_id) REFERENCES ssrc_expert(expert_id)
) COMMENT='专家评分专家表';
```

#### 3.2.3 专家评分要素表 (ssrc_evaluate_indic)
```sql
CREATE TABLE ssrc_evaluate_indic (
    evaluate_indic_id VARCHAR(50) PRIMARY KEY COMMENT '评分要素ID',
    evaluate_expert_id VARCHAR(50) NOT NULL COMMENT '评分专家ID',
    indic_code VARCHAR(30) NOT NULL COMMENT '要素编码',
    indic_name VARCHAR(100) NOT NULL COMMENT '要素名称',
    weight_ratio DECIMAL(5,2) COMMENT '权重比例',
    score DECIMAL(8,2) COMMENT '得分',
    max_score DECIMAL(8,2) COMMENT '满分',
    remark TEXT COMMENT '评分说明',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    
    INDEX idx_evaluate_expert_id (evaluate_expert_id),
    INDEX idx_indic_code (indic_code),
    FOREIGN KEY (evaluate_expert_id) REFERENCES ssrc_evaluate_expert(evaluate_expert_id)
) COMMENT='专家评分要素表';
```

### 3.3 采购管理表

#### 3.3.1 采购申请头表 (sprm_pr_header)
```sql
CREATE TABLE sprm_pr_header (
    pr_header_id VARCHAR(50) PRIMARY KEY COMMENT '采购申请头ID',
    pr_num VARCHAR(60) NOT NULL COMMENT '采购申请单号',
    pr_status VARCHAR(20) NOT NULL COMMENT '申请状态',
    pr_type VARCHAR(20) COMMENT '申请类型',
    company_id VARCHAR(50) NOT NULL COMMENT '公司ID',
    purchase_org_id VARCHAR(50) COMMENT '采购组织ID',
    applicant_id VARCHAR(50) NOT NULL COMMENT '申请人ID',
    apply_date DATE NOT NULL COMMENT '申请日期',
    required_date DATE COMMENT '需求日期',
    total_amount DECIMAL(18,2) COMMENT '总金额',
    currency_code VARCHAR(10) COMMENT '币种',
    remark TEXT COMMENT '备注',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',
    
    UNIQUE KEY uk_pr_num (pr_num),
    INDEX idx_pr_status (pr_status),
    INDEX idx_company_id (company_id),
    INDEX idx_applicant_id (applicant_id),
    INDEX idx_apply_date (apply_date),
    FOREIGN KEY (company_id) REFERENCES hpfm_company(company_id),
    FOREIGN KEY (purchase_org_id) REFERENCES hpfm_purchase_organization(purchase_org_id)
) COMMENT='采购申请头表';
```

#### 3.3.2 采购申请行表 (sprm_pr_line)
```sql
CREATE TABLE sprm_pr_line (
    pr_line_id VARCHAR(50) PRIMARY KEY COMMENT '采购申请行ID',
    pr_header_id VARCHAR(50) NOT NULL COMMENT '采购申请头ID',
    line_number INT NOT NULL COMMENT '行号',
    item_id VARCHAR(50) NOT NULL COMMENT '物料ID',
    item_category_id VARCHAR(50) COMMENT '物料分类ID',
    quantity DECIMAL(18,6) NOT NULL COMMENT '申请数量',
    uom_id VARCHAR(50) NOT NULL COMMENT '计量单位ID',
    unit_price DECIMAL(18,6) COMMENT '单价',
    line_amount DECIMAL(18,2) COMMENT '行金额',
    currency_code VARCHAR(10) COMMENT '币种',
    required_date DATE COMMENT '需求日期',
    delivery_address VARCHAR(500) COMMENT '交货地址',
    line_status VARCHAR(20) COMMENT '行状态',
    remark TEXT COMMENT '备注',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',

    INDEX idx_pr_header_id (pr_header_id),
    INDEX idx_item_id (item_id),
    INDEX idx_line_number (line_number),
    INDEX idx_line_status (line_status),
    FOREIGN KEY (pr_header_id) REFERENCES sprm_pr_header(pr_header_id),
    FOREIGN KEY (item_id) REFERENCES smdm_item(item_id),
    FOREIGN KEY (item_category_id) REFERENCES smdm_item_category(item_category_id),
    FOREIGN KEY (uom_id) REFERENCES smdm_uom(uom_id)
) COMMENT='采购申请行表';
```

#### 3.3.3 采购协议头表 (spcm_pc_header)
```sql
CREATE TABLE spcm_pc_header (
    pc_header_id VARCHAR(50) PRIMARY KEY COMMENT '采购协议头ID',
    pc_num VARCHAR(60) NOT NULL COMMENT '采购协议单号',
    pc_status VARCHAR(20) NOT NULL COMMENT '协议状态',
    pc_type VARCHAR(20) COMMENT '协议类型',
    company_id VARCHAR(50) NOT NULL COMMENT '公司ID',
    purchase_org_id VARCHAR(50) COMMENT '采购组织ID',
    supplier_id VARCHAR(50) NOT NULL COMMENT '供应商ID',
    agreement_date DATE NOT NULL COMMENT '协议日期',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    total_amount DECIMAL(18,2) COMMENT '协议总金额',
    currency_code VARCHAR(10) COMMENT '币种',
    payment_terms VARCHAR(200) COMMENT '付款条件',
    delivery_terms VARCHAR(200) COMMENT '交货条件',
    remark TEXT COMMENT '备注',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',

    UNIQUE KEY uk_pc_num (pc_num),
    INDEX idx_pc_status (pc_status),
    INDEX idx_company_id (company_id),
    INDEX idx_supplier_id (supplier_id),
    INDEX idx_effective_date (effective_date),
    INDEX idx_expiry_date (expiry_date),
    FOREIGN KEY (company_id) REFERENCES hpfm_company(company_id),
    FOREIGN KEY (purchase_org_id) REFERENCES hpfm_purchase_organization(purchase_org_id)
) COMMENT='采购协议头表';
```

### 3.4 供应商管理表

#### 3.4.1 供应商生命周期表 (sslm_life_cycle)
```sql
CREATE TABLE sslm_life_cycle (
    life_cycle_id VARCHAR(50) PRIMARY KEY COMMENT '生命周期ID',
    supplier_company_id VARCHAR(50) NOT NULL COMMENT '供应商公司ID',
    company_id VARCHAR(50) NOT NULL COMMENT '采购方公司ID',
    supplier_tenant_id VARCHAR(50) COMMENT '供应商租户ID',
    current_stage VARCHAR(30) NOT NULL COMMENT '当前阶段',
    stage_status VARCHAR(20) NOT NULL COMMENT '阶段状态',
    requisition_id VARCHAR(50) COMMENT '正在申请中的申请ID',
    registration_date DATE COMMENT '注册日期',
    qualification_date DATE COMMENT '合格日期',
    last_evaluation_date DATE COMMENT '最后评估日期',
    next_evaluation_date DATE COMMENT '下次评估日期',
    risk_level VARCHAR(20) COMMENT '风险等级',
    performance_score DECIMAL(5,2) COMMENT '绩效得分',
    enabled_flag TINYINT(1) DEFAULT 1 COMMENT '启用标识',
    created_by VARCHAR(50) NOT NULL COMMENT '创建人',
    creation_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    last_updated_by VARCHAR(50) COMMENT '最后更新人',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    object_version_number INT NOT NULL DEFAULT 1 COMMENT '行版本号',

    INDEX idx_supplier_company_id (supplier_company_id),
    INDEX idx_company_id (company_id),
    INDEX idx_current_stage (current_stage),
    INDEX idx_stage_status (stage_status),
    FOREIGN KEY (company_id) REFERENCES hpfm_company(company_id)
) COMMENT='供应商生命周期表';
```
```

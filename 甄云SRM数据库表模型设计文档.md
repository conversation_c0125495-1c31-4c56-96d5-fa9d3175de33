# 甄云SRM数据库表模型设计文档

## 1. 概述

本文档基于甄云SRM系统的API接口文档分析，针对「业务对象、组合业务对象、页面个性化」三个核心功能模块，设计完整的数据库表模型关系。该系统主要涵盖供应商关系管理(SRM)的核心业务功能，包括询价管理、采购管理、供应商生命周期管理、专家评分、组织架构等模块。

## 2. 核心业务实体分析

### 2.1 主要业务对象识别

根据API文档分析，系统包含以下核心业务实体：

**询价管理模块：**
- 询价单头 (SRM_SSRC_RFX_HEADER)
- 询价单行-物料 (SRM_SSRC_RFX_LINE_ITEM)  
- 询价单行-供应商 (SRM_SSRC_RFX_LINE_SUPPLIER)

**专家评分模块：**
- 专家库表 (SRM_SSRC_EXPERT)
- 专家评分专家表 (SRM_SSRC_EVALUATE_EXPERT)
- 专家评分要素表 (SRM_SSRC_EVALUATE_INDIC)

**采购管理模块：**
- 采购申请头 (SRM_SPRM_PR_HEADER)
- 采购申请行 (SRM_SPRM_PR_LINE)
- 采购协议头 (SRM_SPCM_PC_HEADER)

**供应商管理模块：**
- 供应商生命周期 (SRM_SSLM_LIFE_CYCLE)
- 供应商生命周期阶段配置 (SRM_SSLM_LIFE_CYCLE_STAGES)
- 潜在升级申请单 (SRM_SSLM_POTENTIAL_HEADER)

**基础数据模块：**
- 物料 (SRM_SMDM_ITEM)
- 计量单位定义 (SRM_SMDM_UOM)
- 自主品类 (SRM_SMDM_ITEM_CATEGORY)

**组织架构模块：**
- 公司信息 (SRM_HPFM_COMPANY)
- 组织 (SRM_HPFM_UNIT)
- 采购组织 (SRM_HPFM_PURCHASE_ORGANIZATION)
- 业务实体 (SRM_HPFM_OPERATION_UNIT)

**用户管理模块：**
- 子账户 (SRM_IAM_USER)

**页面个性化模块：**
- 业务对象 (HMDE_BUSINESS_OBJECT)
- 业务对象字段 (HMDE_BUSINESS_OBJECT_FIELD)
- 业务对象关联关系 (HMDE_BUSINESS_OBJECT_ASSOCIATE)
- 页面个性化配置 (HPFM_UI_CUSTOMIZE_CONFIG)
- 页面单元配置 (HPFM_UNIT_CONFIG)

## 3. 数据库表设计

### 3.1 询价管理表

#### 3.1.1 询价单头表 (ssrc_rfx_header)

| 字段名 | 数据类型 | 长度 | 是否必填 | 主键 | 外键 | 说明 | 业务约束 |
|--------|----------|------|----------|------|------|------|----------|
| rfx_header_id | VARCHAR | 50 | 是 | ✓ | | 询价单头ID | 系统生成UUID |
| domain_id | VARCHAR | 50 | 是 | | | 域ID | 多租户隔离 |
| rfx_num | VARCHAR | 60 | 是 | | | RFX单号 | 业务编码规则生成 |
| rfx_status | VARCHAR | 30 | 是 | | | 询价单状态 | NEW/RELEASE_APPROVING/IN_QUOTATION等 |
| source_category | VARCHAR | 20 | 是 | | | 寻源类型 | RFQ/询价,RFA/竞价,BID/招投标 |
| quotation_type | VARCHAR | 20 | 是 | | | 报价方式 | ONLINE/OFFLINE/ON_OFF |
| round_number | INT | | 是 | | | 版本号 | 默认1，每次调整递增 |
| adjust_times | INT | | 否 | | | 调整次数 | 默认0 |
| company_id | VARCHAR | 50 | 是 | | ✓ | 公司ID | 关联公司信息表 |
| application_scope_flag | TINYINT | 1 | 否 | | | 适用范围标识 | 0/1 |
| auto_defer_flag | TINYINT | 1 | 否 | | | 自动延时标识 | 0/1 |
| check_item_release_flag | TINYINT | 1 | 否 | | | 释放未中标申请数量 | 0/1 |
| closed_flag | TINYINT | 1 | 否 | | | 关闭标识 | 0/1 |
| current_sequence_num | INT | | 否 | | | 当前专家评分组别 | |
| only_allow_all_win_bids | TINYINT | 1 | 否 | | | 仅允许整单中标 | 0/1 |
| sealed_quotation_flag | TINYINT | 1 | 否 | | | 密封报价标志 | 0/1 |
| source_creation_date | DATETIME | | 否 | | | 单据创建时间 | |
| created_by | VARCHAR | 50 | 是 | | | 创建人 | |
| creation_date | DATETIME | | 是 | | | 创建日期 | 系统自动生成 |
| last_updated_by | VARCHAR | 50 | 否 | | | 最后更新人 | |
| last_update_date | DATETIME | | 否 | | | 最后更新时间 | 自动更新 |
| object_version_number | INT | | 是 | | | 行版本号 | 乐观锁控制 |
| remark | TEXT | | 否 | | | 备注 | |

**索引设计：**
- 主键索引：rfx_header_id
- 唯一索引：rfx_num
- 普通索引：rfx_status, company_id, creation_date, source_category

#### 3.1.2 询价单行-物料表 (ssrc_rfx_line_item)

| 字段名 | 数据类型 | 长度 | 是否必填 | 主键 | 外键 | 说明 | 业务约束 |
|--------|----------|------|----------|------|------|------|----------|
| rfx_line_item_id | VARCHAR | 50 | 是 | ✓ | | 询价单行物料ID | 系统生成UUID |
| rfx_header_id | VARCHAR | 50 | 是 | | ✓ | 询价单头ID | 关联询价单头表 |
| item_id | VARCHAR | 50 | 是 | | ✓ | 物料ID | 关联物料表 |
| line_number | INT | | 是 | | | 行号 | 同一询价单内唯一 |
| quantity | DECIMAL | 18,6 | 否 | | | 数量 | 必须大于0 |
| uom_id | VARCHAR | 50 | 否 | | ✓ | 计量单位ID | 关联计量单位表 |
| required_date | DATE | | 否 | | | 需求日期 | |
| delivery_address | VARCHAR | 500 | 否 | | | 交货地址 | |
| created_by | VARCHAR | 50 | 是 | | | 创建人 | |
| creation_date | DATETIME | | 是 | | | 创建日期 | |
| last_updated_by | VARCHAR | 50 | 否 | | | 最后更新人 | |
| last_update_date | DATETIME | | 否 | | | 最后更新时间 | |
| object_version_number | INT | | 是 | | | 行版本号 | |

**索引设计：**
- 主键索引：rfx_line_item_id
- 普通索引：rfx_header_id, item_id, line_number
- 复合索引：(rfx_header_id, line_number)

#### 3.1.3 询价单行-供应商表 (ssrc_rfx_line_supplier)

| 字段名 | 数据类型 | 长度 | 是否必填 | 主键 | 外键 | 说明 | 业务约束 |
|--------|----------|------|----------|------|------|------|----------|
| rfx_line_supplier_id | VARCHAR | 50 | 是 | ✓ | | 询价单行供应商ID | 系统生成UUID |
| rfx_header_id | VARCHAR | 50 | 是 | | ✓ | 询价单头ID | 关联询价单头表 |
| rfx_line_item_id | VARCHAR | 50 | 是 | | ✓ | 询价单行物料ID | 关联询价单行物料表 |
| supplier_id | VARCHAR | 50 | 是 | | | 供应商ID | |
| quoted_price | DECIMAL | 18,6 | 否 | | | 报价 | 必须大于等于0 |
| currency_code | VARCHAR | 10 | 否 | | | 币种 | |
| quote_status | VARCHAR | 20 | 否 | | | 报价状态 | |
| win_bid_flag | TINYINT | 1 | 否 | | | 中标标识 | 0/1 |
| created_by | VARCHAR | 50 | 是 | | | 创建人 | |
| creation_date | DATETIME | | 是 | | | 创建日期 | |
| last_updated_by | VARCHAR | 50 | 否 | | | 最后更新人 | |
| last_update_date | DATETIME | | 否 | | | 最后更新时间 | |
| object_version_number | INT | | 是 | | | 行版本号 | |

**索引设计：**
- 主键索引：rfx_line_supplier_id
- 普通索引：rfx_header_id, rfx_line_item_id, supplier_id, win_bid_flag
- 复合索引：(rfx_header_id, supplier_id)
